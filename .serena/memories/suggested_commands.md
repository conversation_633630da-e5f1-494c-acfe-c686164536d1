# Suggested Commands

## Unity Editor Operations
- **Build Player**: File → Build Settings → Build
- **Addressables**: Window → Asset Management → Addressables → Groups
- **Package Manager**: Window → Package Manager
- **Refresh packages**: Assets → Reimport All
- **Odin Inspector**: Too<PERSON> → <PERSON>din Inspector → Getting Started
- **DOTween Setup**: Tools → Demigiant → DOTween Utility Panel

## System Commands (macOS)
- `ls` - List directory contents
- `find` - Find files and directories
- `grep` - Search text patterns
- `git` - Version control operations
- `cd` - Change directory

## Development Workflow
- **No automated tests**: All testing is manual in Unity Editor
- **No build commands**: Use Unity Editor Build Settings
- **No lint/format commands**: Follow coding conventions manually
- **Manual validation**: Always inform developer of changes needing testing

## Important Notes
- This is a Unity project, not a traditional CLI-based project
- All development happens through Unity Editor
- No package.json, npm, or similar dependency managers
- Dependencies managed through Unity Package Manager
- Manual setup required for new packages, project settings, UI prefabs