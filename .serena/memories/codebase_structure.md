# Codebase Structure

## Assets Organization
- `Assets/Source/`: All C# source code organized by system
- `Assets/Settings/`: ScriptableObject configurations and settings
- `Assets/Prefabs/`: Reusable game objects and prefabs
- `Assets/Scenes/`: Unity scenes (SampleScene, TestCharacterController)
- `Assets/Plugins/`: Third-party libraries (DOTween, Odin Inspector)

## Source Code Systems
- `CameraSystem/`: Virtual camera orchestration
- `CharacterSystem/`: Player movement and character control
- `InteractableSystem/`: Object interaction framework
- `Installers/`: Dependency injection installers (Reflex)
- `InventorySystems/`: Inventory management and UI
- `ItemsSystem/`: Item data management and async loading
- `Recipe/`: Crafting recipes and database
- `TimeSystem/`: Custom time management with interfaces
- `Utils/`: Utilities including object pooling, natural distribution

## Key Files
- `ProjectRootInstaller.cs`: Main DI installer
- `GameConsts.cs`: Global constants and configuration
- `InputSystem_Actions.cs`: Generated input system bindings

## Configuration Files
- `Assets/Settings/Recipes/RecipeDatabase.asset`: Crafting recipes
- `Assets/Settings/ItemsSettings/ItemDataAssetDatabase.asset`: Item definitions
- `Assets/Settings/CharacterMovementSettings/`: Movement configs
- `Assets/Settings/Localization/`: Localization settings