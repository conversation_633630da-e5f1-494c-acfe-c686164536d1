# Coding Style and Conventions

## Naming Conventions
- **Classes**: Pascal<PERSON>ase (e.g., `TimeSystem`, `ProjectRootInstaller`)
- **Methods**: PascalCase (e.g., `UpdateFrame`, `InstallBindings`)
- **Fields**: PascalCase for public, _camelCase for private (e.g., `GlobalTimeScale`, `_timeScales`)
- **Constants**: UPPER_CASE (e.g., `EPSILON`, `MAX_BAG_ITEMS_QUANTITY`)
- **Enums**: PascalCase (e.g., `TimeLayerEnum`, `PlacementLocation`)

## Code Architecture Patterns
- **Dependency Injection**: Reflex container bindings in installer classes
- **Interface Segregation**: Specific interfaces like `IUpdateFrame`, `ILateUpdateFrame`, `IPoolObject`
- **ScriptableObject Configuration**: Settings stored as assets
- **Database Pattern**: Items and recipes use ScriptableObject databases
- **Object Pooling Pattern**: All frequently instantiated UI objects use pooling
- **Event-Based UI Architecture**: Views emit events, controllers subscribe
- **Singleton UI Views**: Register UI views as DI singletons for reuse

## Performance Guidelines
- **Avoid**: `FindObjectOfType`, unnecessary `GetComponent`, `DestroyImmediate`, reflection
- **Prefer**: Object pooling over create/destroy cycles
- **Use**: Interface-based design instead of reflection
- **Constants**: All magic strings/numbers should be in `GameConsts.cs`
- **DRY Principle**: Never duplicate code, use appropriate patterns

## Specific Conventions
- **DOTween**: UI ONLY (other uses require discussion)
- **Async Operations**: Use `ItemDataLoaderManager` for asset loading with reference counting
- **Event Management**: Subscribe/unsubscribe only when UI components are active