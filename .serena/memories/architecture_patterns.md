# Architecture Patterns and Design Guidelines

## Dependency Injection (Reflex)
- **Main installer**: `Assets/Source/Installers/ProjectRootInstaller.cs`
- **System installers**: Each major system has its own installer
- **Pattern**: Use `ContainerBuilder.AddSingleton()` for service registration
- **Avoid**: Manual component lookups, use DI throughout

## Object Pooling Pattern
- **Interface**: `IPoolObject` for pooled objects
- **Manager**: `UIObjectPoolManager` for UI object pooling
- **Pool Names**: Defined as constants in `GameConsts.PoolNames`
- **Lifecycle**: Proper Initialize/Reset/Cleanup in pooled objects
- **Event Cleanup**: Always unsubscribe from events before returning to pool

## Time System Architecture
- **Interfaces**: `IUpdateFrame`, `ILateUpdateFrame` for time subscribers
- **Layers**: `TimeLayerEnum` for different time scales (General, UI, etc.)
- **Descriptors**: `ITimeLayerDescriptor` for layer assignment
- **Pattern**: Subscribe/unsubscribe pattern for time management

## UI Architecture
- **Event-driven**: Views emit events, controllers subscribe
- **Context-agnostic**: Views initialized with data only, no controller references
- **Singleton Views**: UI views registered as DI singletons for reuse
- **Dynamic subscriptions**: Controllers subscribe only when UI is active

## Asset Management
- **Async Loading**: `ItemDataLoaderManager.GetItemAsync()` with reference counting
- **Always Return**: Call `ItemDataLoaderManager.ReturnItem()` when done
- **Addressables**: Used for asset loading and management
- **Caching**: Get item data once per lifecycle and cache it

## Database Pattern
- **ScriptableObjects**: Items and recipes stored as ScriptableObject databases
- **Centralized**: Single database assets for each data type
- **Data-driven**: Game behavior configured through asset databases