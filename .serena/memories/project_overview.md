# Project Overview

**Healing in the Woods** is a 2D cozy game built in Unity 6 (version 6000.2.0b6) using the Universal Rendering Pipeline. 

## Key Characteristics
- **Genre**: 2D cozy game with side-scrolling visual angle
- **Focus**: Easy-to-use mechanics and minimal management systems (not a platformer)
- **Engine**: Unity 6 (6000.2.0b6) with Universal Rendering Pipeline
- **Architecture**: Modular system-based design with dependency injection

## Core Systems
- **Time System**: Custom time management with layered time scaling
- **Camera System**: Virtual camera orchestration
- **Character System**: Player movement and control
- **Interactable System**: Object interaction framework
- **Items System**: Data-driven item management with async loading
- **Inventory Systems**: Inventory management with UI transfer system
- **Recipe System**: Crafting recipes and database

## Key Technologies
- **Reflex**: Dependency injection framework
- **DOTween**: UI animation library (UI ONLY)
- **Odin Inspector**: Enhanced Unity Inspector
- **Unity Addressables**: Asset loading and management
- **Unity Localization**: Multi-language support
- **Unity Input System**: Player input handling
- **UniTask**: Async/await support for Unity