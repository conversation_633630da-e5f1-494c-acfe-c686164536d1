using System;
using UnityEngine;
using HealingInTheWoods.InventorySystems;

namespace HealingInTheWoods
{
    public class UiManager : MonoBehaviour
    {
        [SerializeField] private BagUIController _bagUIController;
        
        private UiState _currentState = UiState.Gameplay;
        
        public UiState CurrentState => _currentState;
        
        public event Action<UiState, UiState> OnStateChanged;

        private void Start()
        {
            InitializeControllers();
        }

        private void InitializeControllers()
        {
            if (_bagUIController != null)
            {
                _bagUIController.gameObject.SetActive(false);
            }
        }

        public void ChangeState(UiState newState)
        {
            if (_currentState == newState) return;

            var previousState = _currentState;
            ExitCurrentState();
            _currentState = newState;
            EnterNewState();
            
            OnStateChanged?.Invoke(previousState, newState);
        }

        private void ExitCurrentState()
        {
            switch (_currentState)
            {
                case UiState.Gameplay:
                    break;
                    
                case UiState.Bag:
                    if (_bagUIController != null)
                        _bagUIController.gameObject.SetActive(false);
                    break;
                    
                case UiState.ChestAndBag:
                case UiState.CauldronAndBag:
                case UiState.Cauldron:
                case UiState.GardenAndBag:
                case UiState.ForestAndBag:
                    break;
            }
        }

        private void EnterNewState()
        {
            switch (_currentState)
            {
                case UiState.Gameplay:
                    break;
                    
                case UiState.Bag:
                    if (_bagUIController != null)
                    {
                        _bagUIController.gameObject.SetActive(true);
                        _bagUIController.RefreshBagDisplay();
                    }
                    break;
                    
                case UiState.ChestAndBag:
                case UiState.CauldronAndBag:
                case UiState.Cauldron:
                case UiState.GardenAndBag:
                case UiState.ForestAndBag:
                    break;
            }
        }

        public void ToggleBag()
        {
            if (_currentState == UiState.Bag)
            {
                ChangeState(UiState.Gameplay);
            }
            else if (_currentState == UiState.Gameplay)
            {
                ChangeState(UiState.Bag);
            }
        }
    }
}