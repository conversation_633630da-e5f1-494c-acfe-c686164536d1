using System.Collections.Generic;
using UnityEngine;
using Reflex.Attributes;
using Hiw.Utils;
using Hiw.InventorySystems;
using Hiw.ItemsSystem;

namespace HealingInTheWoods.InventorySystems
{
    public class BagUIController : MonoBehaviour
    {
        [SerializeField] private RectTransform _bagContainer;
        [SerializeField] private UiItemGroupController _itemGroupPrefab;

        [Inject] private InventoriesManager _inventoriesManager;
        [Inject] private UIObjectPoolManager _poolManager;

        private readonly List<UiItemGroupController> _activeItemGroups = new();
        private readonly Dictionary<(ItemType, PlantId), UiItemGroupController> _itemGroupLookup = new();
        private SimpleObjectPool<UiItemGroupController> _itemGroupPool;

        // Cached positioning data to avoid allocations
        private readonly ResizableArray<GroupPosition> _cachedPositions = new(10);

        // Cached group data to avoid lookups during positioning
        private readonly List<GroupData> _cachedGroupData = new();

        private void Start()
        {
            EnsurePoolInitialized();
        }

        private void EnsurePoolInitialized()
        {
            if (_itemGroupPool == null)
            {
                _itemGroupPool = _poolManager.GetPool(GameConsts.PoolNames.BAG_ITEM_GROUP_UI, _itemGroupPrefab, _bagContainer, 10);
            }
        }

        public void RefreshBagDisplay()
        {
            ClearExistingGroups();
            PopulateBagFromInventory();
        }

        private void ClearExistingGroups()
        {
            EnsurePoolInitialized();

            foreach (var group in _activeItemGroups)
            {
                if (group != null)
                {
                    _itemGroupPool.Return(group);
                }
            }

            _activeItemGroups.Clear();
            _itemGroupLookup.Clear();
            _cachedGroupData.Clear();
        }

        private void PopulateBagFromInventory()
        {
            if (_inventoriesManager?.BagInventory?.Items == null) return;

            var groupedItems = GroupItemsByType();
            CreateItemGroupControllers(groupedItems);
            PositionItemGroups();
        }

        private Dictionary<(ItemType, PlantId), int> GroupItemsByType()
        {
            var groupedItems = new Dictionary<(ItemType, PlantId), int>();

            var items = _inventoriesManager.BagInventory.Items;
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var key = (item.ItemType, item.PlantId);
                if (groupedItems.ContainsKey(key))
                {
                    groupedItems[key] += item.Quantity;
                }
                else
                {
                    groupedItems[key] = item.Quantity;
                }
            }

            return groupedItems;
        }

        private void CreateItemGroupControllers(Dictionary<(ItemType, PlantId), int> groupedItems)
        {
            EnsurePoolInitialized();

            foreach (var kvp in groupedItems)
            {
                var (itemType, plantId) = kvp.Key;
                var quantity = kvp.Value;

                var groupController = _itemGroupPool.Get();

                _activeItemGroups.Add(groupController);
                _itemGroupLookup[kvp.Key] = groupController;

                // Cache group data for positioning and setup
                _cachedGroupData.Add(new GroupData
                {
                    controller = groupController,
                    quantity = quantity,
                    itemType = itemType,
                    plantId = plantId
                });
            }
        }

        private void PositionItemGroups()
        {
            if (_cachedGroupData.Count == 0) return;

            CalculateGroupPositions(_cachedGroupData.Count);

            for (int i = 0; i < _cachedGroupData.Count; i++)
            {
                var groupData = _cachedGroupData[i];
                var position = _cachedPositions.GetStructRef(i);

                var rectTransform = (RectTransform)groupData.controller.transform;
                rectTransform.anchoredPosition = position.center;

                // Setup the group with calculated position and radius - this will trigger visual display
                groupData.controller.Setup(
                    groupData.itemType,
                    groupData.plantId,
                    groupData.quantity,
                    position.center,
                    position.radius,
                    0.3f,
                    DG.Tweening.Ease.OutBack
                );
            }
        }

        private void CalculateGroupPositions(int groupCount)
        {
            _cachedPositions.Clear();

            var containerRect = _bagContainer.rect;
            var containerCenter = Vector2.zero; // Relative to container's pivot

            // Calculate available space with some padding
            var availableWidth = containerRect.width * 0.85f;
            var availableHeight = containerRect.height * 0.85f;
            var maxRadius = Mathf.Min(availableWidth, availableHeight) * 0.15f;

            switch (groupCount)
            {
                case 1:
                    // Single group: Center position
                    _cachedPositions.AddStruct(new GroupPosition
                    {
                        center = containerCenter,
                        radius = maxRadius
                    });
                    break;

                case 2:
                    // Two groups: Opposite sides horizontally
                    var spacing2 = availableWidth * 0.3f;
                    _cachedPositions.Add(new GroupPosition
                    {
                        center = new Vector2(-spacing2, containerCenter.y),
                        radius = maxRadius * 0.8f
                    });
                    _cachedPositions.Add(new GroupPosition
                    {
                        center = new Vector2(spacing2, containerCenter.y),
                        radius = maxRadius * 0.8f
                    });
                    break;

                case 3:
                    // Three groups: Triangle formation
                    var triangleRadius = Mathf.Min(availableWidth, availableHeight) * 0.25f;
                    var groupRadius3 = maxRadius * 0.7f;

                    for (int i = 0; i < 3; i++)
                    {
                        var angle = (i * 2f * Mathf.PI / 3f) - (Mathf.PI / 2f); // Start from top
                        var position = new Vector2(
                            Mathf.Cos(angle) * triangleRadius,
                            Mathf.Sin(angle) * triangleRadius
                        );

                        _cachedPositions.Add(new GroupPosition
                        {
                            center = position,
                            radius = groupRadius3
                        });
                    }
                    break;

                case 4:
                case 5:
                case 6:
                    // Regular polygon distribution
                    var polygonRadius = Mathf.Min(availableWidth, availableHeight) * 0.3f;
                    var groupRadiusPolygon = maxRadius * 0.6f;

                    for (int i = 0; i < groupCount; i++)
                    {
                        var angle = (i * 2f * Mathf.PI / groupCount) - (Mathf.PI / 2f); // Start from top
                        var position = new Vector2(
                            Mathf.Cos(angle) * polygonRadius,
                            Mathf.Sin(angle) * polygonRadius
                        );

                        _cachedPositions.Add(new GroupPosition
                        {
                            center = position,
                            radius = groupRadiusPolygon
                        });
                    }
                    break;

                default:
                    // 7+ groups: Circle distribution with optimal spacing
                    var circleRadius = Mathf.Min(availableWidth, availableHeight) * 0.35f;
                    var groupRadiusCircle = Mathf.Max(maxRadius * 0.4f,
                        (2f * Mathf.PI * circleRadius / groupCount) * 0.3f); // Prevent overlap

                    for (int i = 0; i < groupCount; i++)
                    {
                        var angle = (i * 2f * Mathf.PI / groupCount) - (Mathf.PI / 2f); // Start from top
                        var position = new Vector2(
                            Mathf.Cos(angle) * circleRadius,
                            Mathf.Sin(angle) * circleRadius
                        );

                        _cachedPositions.Add(new GroupPosition
                        {
                            center = position,
                            radius = groupRadiusCircle
                        });
                    }
                    break;
            }
        }

        private struct GroupPosition
        {
            public Vector2 center; // Anchored position relative to container
            public float radius;   // Radius in UI units (pixels at reference resolution)
        }


        private struct GroupData
        {
            public UiItemGroupController controller;
            public int quantity;
            public ItemType itemType;
            public PlantId plantId;
        }

        private void OnDestroy()
        {
            ClearExistingGroups();
        }
    }
}