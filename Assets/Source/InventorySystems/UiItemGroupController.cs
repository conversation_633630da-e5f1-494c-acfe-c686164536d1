using DG.Tweening;
using Hiw.ItemsSystem;
using Hiw.Utils;
using Reflex.Attributes;
using UnityEngine;

namespace Hiw.InventorySystems
{
    public class UiItemGroupController : MonoBehaviour, IPoolObject
    {
        [SerializeField] private UiButtonWithEvents _buttonGroup;
        [SerializeField] private UiItemView _itemViewPrefab;
        [SerializeField] private RectTransform _containerTransform;

        private bool _isSelected;
        private bool _isSubscribed;

        private ItemType _currentItemType;
        private PlantId _currentPlantId;
        private int _currentQuantity;
        private Vector3 _currentGroupCenter;
        private float _currentRangeRadius;
        private float _itemAnimationDuration;
        private Ease _itemAnimationEasing;
        private ResizableArray<UiItemView> _views = new();
        private System.Collections.Generic.List<(UiItemView view, Vector2 position, float hierarchyScore)> _positionDataCache = new();

        [Inject] private UIObjectPoolManager _poolManager;
        private SimpleObjectPool<UiItemView> _itemPool;

        public void Setup(ItemType itemType, PlantId plantId, int quantity, Vector3 groupCenter, float rangeRadius, float animationDuration, Ease animationEasing)
        {
            _currentItemType = itemType;
            _currentPlantId = plantId;
            _currentGroupCenter = groupCenter;
            _currentRangeRadius = rangeRadius;
            _currentQuantity = quantity;
            _itemAnimationDuration = animationDuration;
            _itemAnimationEasing = animationEasing;
        }

        public void UpdateGroup(int quantity, Vector3 groupCenter, float rangeRadius)
        {
            bool shouldUpdate = _currentQuantity != quantity
                || _currentGroupCenter != groupCenter
                || _currentRangeRadius != rangeRadius;

            _currentQuantity = quantity;
            _currentGroupCenter = groupCenter;
            _currentRangeRadius = rangeRadius;

            if (shouldUpdate == false)
                return;
        }

        private void Start()
        {
            SubscribeToButtonEvents();
        }

        private void OnEnable()
        {
            SubscribeToButtonEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromButtonEvents();
        }

        private void OnDestroy()
        {
            UnsubscribeFromButtonEvents();
        }

        private void SubscribeToButtonEvents()
        {
            if (_isSubscribed)
                return;
            _isSubscribed = true;
            _buttonGroup.WhenSelected += WhenSelected_handler;
            _buttonGroup.WhenDeselected += WhenDeselected_handler;
            _buttonGroup.WhenHoverEnter += WhenHoverEnter_handler;
            _buttonGroup.WhenHoverExit += WhenHoverExit_handler;
        }

        private void UnsubscribeFromButtonEvents()
        {
            if (_isSubscribed == false)
                return;
            _isSubscribed = false;
            _buttonGroup.WhenSelected -= WhenSelected_handler;
            _buttonGroup.WhenDeselected -= WhenDeselected_handler;
            _buttonGroup.WhenHoverEnter -= WhenHoverEnter_handler;
            _buttonGroup.WhenHoverExit -= WhenHoverExit_handler;
        }

        private void WhenHoverExit_handler()
        {
            SetGroupSelected(false);
        }

        private void WhenHoverEnter_handler()
        {
            SetGroupSelected(true);
        }

        private void WhenDeselected_handler()
        {
            SetGroupSelected(false);
        }

        private void WhenSelected_handler()
        {
            SetGroupSelected(true);
        }

        private void SetGroupSelected(bool value)
        {
            if (_isSelected == value)
                return;

            _isSelected = value;
            if (_views.Count != 0)
            {
                for (int i = 0; i < _views.Count; i++)
                {
                    _views[i].SetOutlineVisible(_isSelected);
                }
            }
        }

        private void UpdateGroup()
        {
            EnsurePoolInitialized();
            AdjustViewCount();
            PositionAllViews();
        }

        private void EnsurePoolInitialized()
        {
            if (_itemPool == null)
            {
                _itemPool = _poolManager.GetPool(GameConsts.PoolNames.BAG_ITEM_UI, _itemViewPrefab, _containerTransform, 10);
            }
        }

        private void AdjustViewCount()
        {
            int currentCount = _views.Count;

            if (_currentQuantity > currentCount)
            {
                // Add more views
                int viewsToAdd = _currentQuantity - currentCount;
                for (int i = 0; i < viewsToAdd; i++)
                {
                    var itemView = _itemPool.Get();
                    itemView.Setup(_currentItemType, _currentPlantId, _itemAnimationDuration, _itemAnimationEasing, _isSelected);
                    _views.Add(itemView);
                }
            }
            else if (_currentQuantity < currentCount)
            {
                // Remove excess views
                int viewsToRemove = currentCount - _currentQuantity;
                for (int i = 0; i < viewsToRemove; i++)
                {
                    int lastIndex = _views.Count - 1;
                    var itemView = _views[lastIndex];
                    _views.RemoveAt(lastIndex);
                    _itemPool.Return(itemView);
                }
            }
        }

        public void OnGetFromPool()
        {
            // Reset state variables
            _isSelected = false;
            _isSubscribed = false;

            // Clear cached data
            _views.Clear();
            _positionDataCache.Clear();

            // Reset current data
            _currentItemType = default;
            _currentPlantId = default;
            _currentQuantity = 0;
            _currentGroupCenter = Vector3.zero;
            _currentRangeRadius = 0f;
            _itemAnimationDuration = 0f;
            _itemAnimationEasing = Ease.Linear;
        }

        public void OnReturnToPool()
        {
            // Unsubscribe from events
            UnsubscribeFromButtonEvents();

            // Return all item views to their pool
            if (_itemPool != null && _views.Count > 0)
            {
                for (int i = _views.Count - 1; i >= 0; i--)
                {
                    var itemView = _views[i];
                    if (itemView != null)
                    {
                        _itemPool.Return(itemView);
                    }
                }
            }

            // Clear collections
            _views.Clear();
            _positionDataCache.Clear();

            // Reset state
            _isSelected = false;
            _isSubscribed = false;

            // Reset pool reference (will be re-initialized when needed)
            _itemPool = null;
        }

        private void PositionAllViews()
        {
            // Ensure cache has correct count
            _positionDataCache.Clear();

            // Calculate positions and hierarchy scores
            for (int i = 0; i < _views.Count; i++)
            {
                var offset = NaturalDistributionUtility.GetNaturalDistributionOffset(i, _views.Count, _currentRangeRadius);
                var worldPosition = _currentGroupCenter + (Vector3)offset;

                // Calculate hierarchy score: negative x (leftmost) and positive y (topmost) get higher priority
                float hierarchyScore = -worldPosition.x + worldPosition.y;

                _positionDataCache.Add((_views[i], offset, hierarchyScore));
            }

            // Sort by hierarchy score (descending - highest score = highest in hierarchy)
            _positionDataCache.Sort((a, b) => b.hierarchyScore.CompareTo(a.hierarchyScore));

            // Apply positions and set transform hierarchy order
            for (int i = 0; i < _positionDataCache.Count; i++)
            {
                var (view, position, _) = _positionDataCache[i];
                view.transform.localPosition = _currentGroupCenter + (Vector3)position;
                view.transform.SetSiblingIndex(i); // Higher index = rendered on top
            }
        }
    }
}