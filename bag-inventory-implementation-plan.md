# Bag Inventory Implementation Plan

## Overview
Create a comprehensive UI state management system with focus on bag inventory functionality. The system should support multiple UI states and provide dynamic positioning for inventory item groups.

## Implementation Steps

### 1. Create UI State Management Foundation - DONE
- **UiState Enum**: Define all planned UI states
  - `Gameplay`, `Bag`, `ChestAndBag`, `CauldronAndBag`, `<PERSON><PERSON><PERSON>`, `GardenAndBag`, `ForestAndBag`
- **UiManager Class**: Central state coordinator
  - State machine implementation
  - State transition methods
  - References to UI controllers

### 2. Implement Bag UI Controller - DONE
- **BagUIController Class**: Manages bag-specific UI logic
  - Integration with `InventoriesManager.BagInventory`
  - Dynamic creation/management of `UiItemGroupController` instances
  - Item group lifecycle management

### 3. Dynamic Positioning System
- **Geometric Distribution Algorithm**: Position item groups based on count
  - 1 group: Center position
  - 2 groups: Opposite sides
  - 3 groups: Triangle formation
  - 4-6 groups: Regular polygon distribution
  - 7+ groups: Circle distribution with optimal spacing

### 4. Player Input Integration
- **Input Handling**: Add bag toggle functionality
  - Input binding for bag open/close
  - Integration with UiManager state transitions
  - Player controller communication

### 5. UI Integration and Polish
- **State Transitions**: Smooth transitions between states
- **Visual Feedback**: Appropriate UI animations and feedback
- **Testing and Validation**: Manual testing of all functionality

## Key Dependencies
- Existing `UiItemGroupController` and `UiItemView` classes
- `InventoriesManager` for data access
- Unity Input System for player controls
- DOTween for UI animations (if needed)

## Technical Considerations
- Use Reflex DI for proper dependency injection
- Follow existing code patterns and conventions
- Implement proper object pooling where applicable
- Ensure clean separation of concerns between UI and data layers